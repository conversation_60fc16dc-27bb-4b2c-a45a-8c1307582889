package com.mobile.anchor.app.ui.viewmodels

import android.app.Application
import android.content.Intent
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.bdc.android.library.utils.ActivityManager
import com.mobile.anchor.app.data.model.ChatTipBean
import com.mobile.anchor.app.data.model.RemoteCallInviteCancelExtend
import com.mobile.anchor.app.data.model.RemoteCallInviteExtend
import com.mobile.anchor.app.data.model.RemoteCallSettleExtend
import com.mobile.anchor.app.data.model.WebSocketMessage
import com.mobile.anchor.app.data.model.WebSocketResponse
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.socket.WebSocketManager
import com.mobile.anchor.app.socket.WebSocketMessageSender
import com.mobile.anchor.app.ui.call.CallVideoChatActivity
import com.mobile.anchor.app.utils.Constants
import com.mobile.anchor.app.utils.ContextHolder
import com.mobile.anchor.app.utils.RongYunUtil
import com.mobile.anchor.app.utils.fromJson
import com.mobile.anchor.app.utils.toJson
import io.rong.imkit.IMCenter
import io.rong.imkit.conversation.extension.parsemessage.MikChatAskGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatGiftMessage
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageBlockType
import io.rong.message.CommandMessage
import io.rong.message.TextMessage
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.util.Collections
import kotlin.code

class SocketMessageViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        const val TAG = "SocketMessageViewModel"
    }

    private var unReadCallback: UnReadCallback = UnReadCallback(this)
    private val _pageEvents = SharedFlowEvents<MessagePageEvent>()
    val pageEvents = _pageEvents.asSharedFlow()

    // 用于存储已处理消息的 seq，防止重复处理，并限制最大数量
    private val processedMessageSeqs: MutableMap<String, Long> = Collections.synchronizedMap(
        object : LinkedHashMap<String, Long>(500, 0.75f, true) {
            override fun removeEldestEntry(eldest: MutableMap.MutableEntry<String, Long>?): Boolean {
                return size > 500 // 当容量超过500时，移除最老的条目
            }
        }
    )

    private val mOnReceiveMessageListener: RongIMClient.OnReceiveMessageWrapperListener =
        object : RongIMClient.OnReceiveMessageWrapperListener() {
            override fun onReceived(
                message: Message, left: Int, hasPackage: Boolean, offline: Boolean
            ): Boolean {
                LogX.d(TAG, "onReceivedMessageaa1  ：  " + message.content)
                LogX.d(TAG, "onReceivedMessageaa2  ：  " + message.content.extra)
                LogX.d(TAG, "onReceivedMessageaa3  ：  " + message.extra)
                LogX.d(TAG, "onReceivedMessageaa4  ：  " + message.content.jsonDestructInfo)
                updateCommandMessage(message)
//                updateCustomServiceCache(message)
                refreshUnreadMsg()
//                updateMikChatMessage(message, userInfo)
                updateGetGiftMikChatMessage(message)
                updateSystemChatMessage(message)
                updateTextMikChatMessage(message)
                return false
            }
        }

    // 静态内部类实现消息块回调，避免内存泄漏
    private class MessageBlockCallback : IRongCoreCallback.ResultCallback<Message?>() {
        override fun onSuccess(t: Message?) {
            t?.let {
                val expansion = hashMapOf<String, String>(Constants.MESSAGE_EXPEND_KEY_BLOCK to "1")
                RongIMClient.getInstance().updateMessageExpansion(expansion, it.uId, MessageExpansionCallback(it))
            }
        }

        override fun onError(e: IRongCoreEnum.CoreErrorCode?) {
            // 错误处理
        }
    }

    // 静态内部类实现消息扩展回调，避免内存泄漏
    private class MessageExpansionCallback(private val message: Message) : RongIMClient.OperationCallback() {
        override fun onSuccess() {
            LogX.e("updateMessageExpansion success")
            val expansion = hashMapOf<String, String>(Constants.MESSAGE_EXPEND_KEY_BLOCK to "1")
            message.setExpansion(expansion)
            IMCenter.getInstance().refreshMessage(message)
        }

        override fun onError(errorCode: RongIMClient.ErrorCode?) {
            LogX.e("updateMessageExpansion error ${errorCode}")
        }
    }


//    private val translationResultListener = TranslationResultListener { code, result ->
//        if (code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_INVALID_AUTH_TOKEN.code || code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_AUTH_FAILED.code || code == IRongCoreEnum.CoreErrorCode.RC_TRANSLATION_CODE_SERVER_AUTH_FAILED.code) {
//            getRongYunTranslateToken()
//        }
//    }

    fun init() {
        IMCenter.getInstance().addAsyncOnReceiveMessageListener(mOnReceiveMessageListener) //融云消息监听
        RongCoreClient.getInstance().setMessageBlockListener {//处理消息拦截违规
            LogX.e("vvvvvvvvvvvv block message: ${it.targetId} ${it.type} ${it.blockMsgUId}")
            if (it.type == MessageBlockType.BLOCK_THIRD_PATY) {//三方数美判定照片违规
                // 使用静态内部类实现回调，避免内存泄漏
                RongCoreClient.getInstance().getMessageByUid(it.blockMsgUId, MessageBlockCallback())
            }
        }

        // 监听 WebSocket 消息
        viewModelScope.launch {
            WebSocketManager.getInstance().webSocketEventFlow.collect { event ->
                when (event) {
                    is WebSocketManager.WebSocketEvent.OnMessage -> {
                        try {
                            val webSocketResponse = event.text.fromJson<WebSocketResponse>()
                            if (webSocketResponse?.code == Constants.ErrorCode.TOKEN_EXPIRED) {
                                FlowBus.with<Int>(Constants.TOKEN_EXPIRED)
                                    .post(Constants.ErrorCode.TOKEN_EXPIRED)
                            } else {
                                dispatchMessage(
                                    event.text,
                                    webSocketResponse?.data?.seq,
                                    "websocket"
                                )
                            }
                        } catch (e: Exception) {
                            LogX.e("解析 WebSocket 消息失败，无法获取 seq: ${e.message}, 消息内容: ${event.text}")
                        }
                    }

                    else -> {
                        // 处理其他 WebSocket 事件，例如连接成功、关闭、失败等
                        LogX.d("WebSocketEvent: ${event.javaClass.simpleName}")
                    }
                }
            }
        }
    }

    private fun updateCommandMessage(message: Message) {
        if (message.content is CommandMessage) {
            val commandMessage: CommandMessage = message.content as CommandMessage
            LogX.e(TAG, "commandMessage: ${commandMessage.name}     ${commandMessage.data}")
            // 尝试从 commandMessage.data 中解析出 cmd 和 seq
            try {
                val dataJson = commandMessage.data
                val webSocketMessage = dataJson.fromJson<WebSocketMessage>()
                val cmd = webSocketMessage?.cmd
                val seq = webSocketMessage?.seq

                if (cmd != null && seq != null) {
                    // 封装成 WebSocketResponse 格式的 JSON 字符串
                    val fullMessageJson = "{\"code\":0,\"data\":$dataJson,\"message\":\"\"}"
                    dispatchMessage(fullMessageJson, seq, "RongYun")
                } else {
                    LogX.e(TAG, "无法从 CommandMessage.data 中解析 cmd 或 seq: $dataJson")
                }
            } catch (e: Exception) {
                LogX.e(TAG, "解析 CommandMessage.data 失败: ${e.message}")
            }
        }
    }

    /**
     * 统一处理和分发 WebSocket 消息，并进行去重
     * @param messageJson 消息的 JSON 字符串
     * @param seq 消息的唯一序列号，用于去重。如果为 null，则不进行去重。
     */
    private fun dispatchMessage(messageJson: String, seq: String? = null, fromType: String?) {

        LogX.e(TAG, "dispatchMessage:  $fromType $seq")
        if (seq != null) {
            if (processedMessageSeqs.containsKey(seq)) {
                LogX.d(TAG, "消息已处理，跳过重复消息: fromtype=$fromType  seq=$seq")
                return
            }
            processedMessageSeqs[seq] = System.currentTimeMillis()
        }

        try {
            val webSocketResponse = messageJson.fromJson<WebSocketResponse>()
            val webSocketMessage = webSocketResponse?.data
            if (webSocketMessage == null) {
                LogX.e(TAG, "解析 WebSocket 消息为 null 或 data 字段为 null: $messageJson")
                return
            }
            when (webSocketMessage.cmd) {
                Constants.PushCmd.PUSH_CMD_REMOTE_CALL_INVITE -> {
                    val inviteExtend = webSocketMessage.extend?.fromJson<RemoteCallInviteExtend>()
                    if (inviteExtend != null) {
                        handleRemoteCallInvite(webSocketMessage, inviteExtend)
                    } else {
                        LogX.e(
                            TAG,
                            "解析 remote_call_invite 的 extend 失败: ${webSocketMessage.extend}"
                        )
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_CALL_INVITE_CANCEL -> {
                    val cancelExtend =
                        webSocketMessage.extend?.fromJson<RemoteCallInviteCancelExtend>()
                    if (cancelExtend != null) {
                        handleRemoteCallInviteCancel(webSocketMessage, cancelExtend)
                    } else {
                        LogX.e(
                            TAG,
                            "解析 remote_call_invite_cancel 的 extend 失败: ${webSocketMessage.extend}"
                        )
                    }
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_PULL_LOG -> {
                    LogX.forceUpload()
                }

                Constants.PushCmd.PUSH_CMD_REMOTE_CALL_SETTLE -> {
                    val settleExtend = webSocketMessage.extend?.fromJson<RemoteCallSettleExtend>()
                    if (settleExtend != null) {
                        handleRemoteCallSettle(webSocketMessage, settleExtend)
                    } else {
                        LogX.e(
                            TAG,
                            "解析 remote_call_settle 的 extend 失败: ${webSocketMessage.extend}"
                        )
                    }
                }

                else -> {
                    LogX.d(TAG, "未知 WebSocket 消息类型: ${webSocketMessage.cmd}")
                }
            }
        } catch (e: Exception) {
            LogX.e(TAG, "解析 WebSocket 消息失败: ${e.message}, 消息内容: $messageJson")
        }
    }

    /**
     * 被叫收到主叫邀请
     */
    private fun handleRemoteCallInvite(message: WebSocketMessage, extend: RemoteCallInviteExtend) {
        LogX.d(TAG, "收到 remote_call_invite 消息: $message, extend: $extend")
        // 将 RemoteCallInviteExtend 对象转换为 JSON 字符串
        val remoteCallInviteExtendJson = extend.toJson()
        LogX.d(TAG, "RemoteCallInviteExtend JSON: $remoteCallInviteExtendJson")

        //TODO 这里如果主播一直不接电话，在某个时间段内，会收到很多条邀请的消息，只处理一次收到的，其他的回复给服务端拒绝
        if (ActivityManager.current is CallVideoChatActivity) {
            val currentCallId = (ActivityManager.current as CallVideoChatActivity).getCallId()
            WebSocketMessageSender.sendCallRefuseMessage(
                extend.peerUID,
                extend.callID,
                currentCallId,
                Constants.WebSocketParamValue.CALL_REFUSE_REASON_ALREADY_IN_CALL,
                "anchor already has invite"
            )
            return
        }

        // 跳转到 CallVideoChatActivity 页面，并传递 JSON 字符串
        val intent = Intent(ContextHolder.context, CallVideoChatActivity::class.java).apply {
            putExtra("remoteCallInviteExtend", remoteCallInviteExtendJson)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK) // 从非Activity上下文启动Activity需要此标志
        }
        ContextHolder.context.startActivity(intent)
    }

    /**
     * 被叫收到主叫取消通话
     */
    private fun handleRemoteCallInviteCancel(
        message: WebSocketMessage,
        extend: RemoteCallInviteCancelExtend
    ) {
        LogX.d(TAG, "收到 remote_call_invite_cancel 消息: $message, extend: $extend")
        viewModelScope.launch {
            FlowBus.with<Int>(Constants.PushCmd.PUSH_CMD_REMOTE_CALL_INVITE_CANCEL)
                .post(extend.callID)
        }
    }

    /**
     * 通话结算消息
     */
    private fun handleRemoteCallSettle(message: WebSocketMessage, extend: RemoteCallSettleExtend) {
        LogX.d(TAG, "收到 remote_call_settle 消息: $message, extend: $extend")
        viewModelScope.launch {
            FlowBus.with<RemoteCallSettleExtend>(Constants.PushCmd.PUSH_CMD_REMOTE_CALL_SETTLE)
                .post(extend)
        }
    }

    // 静态内部类实现回调，避免内存泄漏
    private class UnReadCallback(viewModel: SocketMessageViewModel) :
        RongIMClient.ResultCallback<Int>() {
        private val weakViewModel = WeakReference(viewModel)

        override fun onSuccess(unReadCount: Int?) {
            val viewModel = weakViewModel.get() ?: return
            unReadCount?.let {
                viewModel.viewModelScope.launch {
                    viewModel._pageEvents.setEvent(MessagePageEvent.RefreshBottomBarUnReadMsg(it))
                }
            }
        }

        override fun onError(e: RongIMClient.ErrorCode?) {
            // 错误处理
        }
    }

    fun refreshUnreadMsg() {
        RongIMClient.getInstance().getTotalUnreadCount(unReadCallback)
    }

    private fun updateSystemChatMessage(message: Message) {
        if (message.senderUserId == Constants.RONG_YUN_ID_SYSTEM) {
            RongYunUtil.cacheSystemNoticeInfo()
        }
    }

    private fun updateTextMikChatMessage(message: Message) {
        if (message.content is TextMessage) {
            viewModelScope.launch {
                message.content?.let {
                    val textMessage = message.content as TextMessage
                    val chatTipBean = ChatTipBean(
                        message.senderUserId, nickName = "", content = textMessage.content ?: ""
                    )
                    FlowBus.with<ChatTipBean>(Constants.PUSH_TYPE_TEXT_MESSAGE)
                        .post(chatTipBean)
                }
            }
        }
    }

    private fun updateGetGiftMikChatMessage(message: Message) {
        if (message.content is MikChatGiftMessage) {
            val mikChatMessage: MikChatGiftMessage = message.content as MikChatGiftMessage
            val chatTipBean = ChatTipBean(
                message.senderUserId, nickName = "", headFileName = mikChatMessage.icon
            )
            viewModelScope.launch {
                FlowBus.with<ChatTipBean>(Constants.PUSH_TYPE_REQUEST_GIFT)
                    .post(chatTipBean)
            }
            return
        }
    }
}

sealed class MessagePageEvent {
    data class RefreshBottomBarUnReadMsg(val count: Int) : MessagePageEvent()
}