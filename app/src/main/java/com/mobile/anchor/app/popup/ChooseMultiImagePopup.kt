package com.mobile.anchor.app.popup

import android.widget.ImageView
import androidx.activity.ComponentActivity
import androidx.core.content.ContextCompat
import com.bdc.android.library.imageloader.ImageLoader
import com.luck.lib.camerax.SimpleCameraX
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.luck.picture.lib.style.PictureSelectorStyle
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BottomPopupView
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupChoooseAvatarBinding
import com.mobile.anchor.app.utils.GlideImageEngine
import com.mobile.anchor.app.utils.UCropImageCropEngine
import io.rong.imkit.picture.entity.LocalMedia
import kotlin.apply


class ChooseMultiImagePopup(
    private val activity: ComponentActivity, private val onClick: (ArrayList<LocalMedia?>?) -> Unit
) : BottomPopupView(activity) {
    companion object {
        private const val MAX_SEL_SIZE = 9
    }

    override fun getImplLayoutId(): Int = R.layout.popup_chooose_avatar

    override fun initPopupContent() {
        super.initPopupContent()

        val binding = PopupChoooseAvatarBinding.bind(popupContentView)
        binding.tvTakePicture.setOnClickListener {
            PictureSelector.create(activity).openCamera(SelectMimeType.ofImage())
                //是否开启原图功能 -- 这里不开启原图功能
                .isOriginalControl(false)
                .setCameraInterceptListener { fragment, cameraMode, requestCode ->
                    val camera: SimpleCameraX = SimpleCameraX.of()
                    camera.setCameraMode(cameraMode)

                    //视频帧率，越高视频体积越大
                    camera.setVideoFrameRate(25)

                    //bit率， 越大视频体积越大
                    camera.setVideoBitRate(3 * 1024 * 1024)

                    //是否显示录制时间  -- 设置为true
                    camera.isDisplayRecordChangeTime(true)

                    //是否支持手指点击对焦  设置true
                    camera.isManualFocusCameraPreview(true)

                    //支持手指缩放相机   设置true
                    camera.isZoomCameraPreview(true)

                    //拍照自定义输出路径
                    //                        camera.setOutputPathDir(com.oevhenmi.picture.selectgvimage.picture.PictureSelectorTools.getSandboxPath())
                    camera.setImageEngine { context, url, imageView ->
                        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP)
                        ImageLoader.with(context).load(url).into(imageView)
                    }
                    camera.start(fragment.requireContext(), fragment, requestCode)
                }.setCropEngine(UCropImageCropEngine())
                .forResult(object : OnResultCallbackListener<LocalMedia?> {
                    override fun onResult(result: ArrayList<LocalMedia?>) {
                        onClick.invoke(result)
                        dismiss()
                    }

                    override fun onCancel() {
                        dismiss()
                    }
                })
        }
        binding.tvGallery.setOnClickListener {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//                dismissWith {  onClick.invoke(null) }
//                ToastUtil.show(context.getString(R.string.select_image_max_tips))
//                PictureSelector.create(activity).openSystemGallery(SelectMimeType.ofImage())
//                    .setSelectionMode(SelectModeConfig.MULTIPLE)
//                    .forSystemResult(object : OnResultCallbackListener<LocalMedia?> {
//                        override fun onResult(result: ArrayList<LocalMedia?>?) {
//                            result?.let {
//                                if (it.size > MAX_SEL_SIZE) {
//                                    // 手动限制选择数量
//                                    ToastUtil.show(context.getString(R.string.select_image_limit_tips))
//                                    val limitedResult = ArrayList(it.take(MAX_SEL_SIZE))
//                                    onClick.invoke(limitedResult)
//                                } else {
//                                    onClick.invoke(it)
//                                }
//                            }
//                            dismiss()
//                        }
//                        override fun onCancel() {
//                            dismiss()
//                        }
//                    })
//            } else {
            PictureSelector.create(activity).openGallery(SelectMimeType.ofImage())
                    .setMaxSelectNum(MAX_SEL_SIZE).setImageEngine(GlideImageEngine()).isDisplayCamera(false)
//                    .setCropEngine(UCropImageCropEngine())
                    .setSelectorUIStyle(PictureSelectorStyle().apply {
                        titleBarStyle = titleBarStyle.apply {
                            titleBackgroundColor =
                                ContextCompat.getColor(context, R.color.background)
                        }
                        selectMainStyle = selectMainStyle.apply {
                            selectTextColor = ContextCompat.getColor(context, R.color.colorAccent)
                        }
                        bottomBarStyle = bottomBarStyle.apply {
                            bottomNarBarBackgroundColor =
                                ContextCompat.getColor(context, R.color.background)
                            bottomPreviewSelectTextColor =
                                ContextCompat.getColor(context, R.color.color_B452FF)
                            bottomSelectNumResources = R.drawable.shape_9f2af8_50_20
                            bottomSelectNumTextColor = ContextCompat.getColor(
                                context, R.color.white
                            )
                        }
                    }).forResult(object : OnResultCallbackListener<LocalMedia?> {
                        override fun onResult(result: ArrayList<LocalMedia?>) {
                            onClick.invoke(result)
                            dismiss()
                        }

                        override fun onCancel() {
                            dismiss()
                        }
                    })
//            }
        }

        binding.tvCancel.setOnClickListener { dismiss() }
    }
}

fun showChooseMultiImagesPopup(activity: ComponentActivity, block: (ArrayList<LocalMedia?>?) -> Unit) {
    XPopup.Builder(activity).asCustom(ChooseMultiImagePopup(activity, block)).show()
}