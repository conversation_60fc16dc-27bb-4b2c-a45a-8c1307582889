package com.mobile.anchor.app.ui.popup

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.text.input.KeyboardType
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.ui.components.BottomSelectDialog
import com.mobile.anchor.app.ui.components.ConfirmDialog
import com.mobile.anchor.app.ui.components.DatePickerDialog
import com.mobile.anchor.app.ui.components.InputDialog
import com.mobile.anchor.app.ui.components.LoadingDialog
import com.mobile.anchor.app.ui.theme.AnchorTheme
import java.util.Calendar

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/13 18:08
 * @description : Compose弹框封装，支持在普通Activity中使用
 */
class ComposeDialogFragment : DialogFragment() {

    private var composeContent: (@Composable (dismiss: () -> Unit) -> Unit)? = null
    private var onDismissCallback: (() -> Unit)? = null

    companion object {
        fun newInstance(
            content: @Composable (dismiss: () -> Unit) -> Unit, onDismiss: (() -> Unit)? = null
        ): ComposeDialogFragment {
            return ComposeDialogFragment().apply {
                this.composeContent = content
                this.onDismissCallback = onDismiss
            }
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).apply {
//            requestWindowFeature(Window.FEATURE_NO_TITLE)
            window?.apply {

//                clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
//                addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
//                decorView.systemUiVisibility =
//                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                statusBarColor = Color.TRANSPARENT // 或你的透明色

//                statusBarColor = ContextCompat.getColor(requireContext(), R.color.color_EC12E2)

                // 如果你希望状态栏图标是深色或浅色（取决于背景）
//                WindowInsetsControllerCompat(this, decorView).isAppearanceLightStatusBars = true // or false

            }
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                AnchorTheme {
                    composeContent?.invoke {
                        // 关闭DialogFragment
                        dismiss()
                    }
                }
            }
        }
    }

    override fun getTheme(): Int {
        return R.style.AnchorDialogTheme
    }

    override fun onDismiss(dialog: android.content.DialogInterface) {
        super.onDismiss(dialog)
        onDismissCallback?.invoke()
    }

    /**
     * 安全显示弹框
     */
    fun showSafely(activity: FragmentActivity, tag: String = "ComposeDialogFragment") {
        if (!activity.isFinishing && !activity.isDestroyed) {
            try {
                show(activity.supportFragmentManager, tag)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}

/**
 * ComposePopup工具类，提供静态方法调用
 */
object ComposePopup {

    /**
     * 显示确认弹框
     */
    @JvmStatic
    fun showConfirmDialog(
        context: Context,
        title: String = context.getString(R.string.reminder),
        content: String = "",
        confirmText: String = context.getString(R.string.rc_confirm),
        cancelText: String = context.getString(R.string.rc_cancel),
        showConfirmButton: Boolean = true,
        showCancelButton: Boolean = true,
        dismissOnBackPress: Boolean = true,
        dismissOnClickOutside: Boolean = true,
        onConfirm: () -> Unit = {},
        onCancel: () -> Unit = {},
        onDismiss: () -> Unit = {}
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity
        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                var visible by remember { mutableStateOf(true) }

                ConfirmDialog(
                    visible = visible,
                    title = title,
                    content = content,
                    confirmText = confirmText,
                    cancelText = cancelText,
                    showConfirmButton = showConfirmButton,
                    showCancelButton = showCancelButton,
                    dismissOnBackPress = dismissOnBackPress,
                    dismissOnClickOutside = dismissOnClickOutside,
                    onConfirm = {
                        onConfirm()
                        dismiss()
                    },
                    onCancel = {
                        onCancel()
                        dismiss()
                    },
                    onDismiss = {
                        if (dismissOnBackPress || dismissOnClickOutside) {
                            onDismiss()
                            dismiss()
                        }
                    })
            }, onDismiss = onDismiss
        )

        activity?.let { dialogFragment.showSafely(it) }
        return dialogFragment
    }

    /**
     * 显示底部选择弹框
     */
    @JvmStatic
    fun showBottomSelectDialog(
        context: Context,
        title: String = "",
        options: List<String>,
        onOptionSelected: (Int, String) -> Unit,
        onDismiss: () -> Unit = {}
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                var visible by remember { mutableStateOf(true) }

                BottomSelectDialog(
                    visible = visible,
                    title = title,
                    options = options,
                    onOptionSelected = { index, option ->
                        onOptionSelected(index, option)
                        dismiss()
                    },
                    onDismiss = {
                        onDismiss()
                        dismiss()
                    })
            }, onDismiss = onDismiss
        )

        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    /**
     * 显示输入弹框
     */
    @JvmStatic
    fun showInputDialog(
        context: Context,
        title: String = "",
        content: String = "",
        inputValue: String = "",
        inputPlaceholder: String = "",
        inputLabel: String = "",
        maxLength: Int = 0,
        keyboardType: KeyboardType = KeyboardType.Text,
        confirmText: String = "Confirm",
        cancelText: String = "Cancel",
        showCancelButton: Boolean = true,
        onConfirm: (String) -> Unit,
        onCancel: () -> Unit = {},
        onDismiss: () -> Unit = {},
        inputValidator: ((String) -> String?)? = null
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                var visible by remember { mutableStateOf(true) }
                var currentInputValue by remember { mutableStateOf(inputValue) }

                InputDialog(
                    visible = visible,
                    title = title,
                    content = content,
                    inputValue = currentInputValue,
                    inputPlaceholder = inputPlaceholder,
                    inputLabel = inputLabel,
                    maxLength = maxLength,
                    keyboardType = keyboardType,
                    confirmText = confirmText,
                    cancelText = cancelText,
                    showCancelButton = showCancelButton,
                    onInputChange = { currentInputValue = it },
                    onConfirm = { input ->
                        onConfirm(input)
                        dismiss()
                    },
                    onCancel = {
                        onCancel()
                        dismiss()
                    },
                    onDismiss = {
                        onDismiss()
                        dismiss()
                    },
                    inputValidator = inputValidator
                )
            }, onDismiss = onDismiss
        )

        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    /**
     * 显示日期选择弹框
     */
    @JvmStatic
    fun showDatePickerDialog(
        context: Context,
        title: String = "Select Date",
        initialDate: Calendar? = null,
        onDateSelected: (Calendar) -> Unit,
        onDismiss: () -> Unit = {}
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                var visible by remember { mutableStateOf(true) }

                DatePickerDialog(
                    visible = visible,
                    title = title,
                    initialDate = initialDate,
                    onDateSelected = { date ->
                        onDateSelected(date)
                        dismiss()
                    },
                    onDismiss = {
                        onDismiss()
                        dismiss()
                    })
            }, onDismiss = onDismiss
        )

        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    /**
     * 显示加载弹框
     */
    @JvmStatic
    fun showLoadingDialog(
        context: Context, message: String = "Loading...", cancelable: Boolean = false
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                LoadingDialog(
                    visible = true, message = message
                )
            }).apply {
            isCancelable = cancelable
        }

        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    /**
     * 显示自定义内容弹框
     */
    @JvmStatic
    fun showCustomDialog(
        context: Context, content: @Composable (dismiss: () -> Unit) -> Unit
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                content(dismiss)
            })

        dialogFragment.showSafely(activity)
        return dialogFragment
    }
}
