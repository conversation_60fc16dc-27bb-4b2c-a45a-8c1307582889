package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * 验证码输入类型
 */
enum class CaptchaType {
    NUMBER,    // 纯数字
    LETTER,    // 纯字母
    MIXED      // 数字和字母混合
}

/**
 * 验证码输入组件
 *
 * @param length 验证码位数，默认6位
 * @param captchaType 验证码类型，默认数字
 * @param value 当前输入的验证码值
 * @param onValueChange 值变化回调
 * @param onComplete 输入完成回调（当输入满指定位数时触发）
 * @param modifier 修饰符
 */
@Composable
fun CaptchaTextField(
    length: Int = 6,
    captchaType: CaptchaType = CaptchaType.NUMBER,
    value: String = "",
    onValueChange: (String) -> Unit = {},
    onComplete: (String) -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 颜色定义
    val focusedColor = Color(0xFF9F2AF8)
    val unFocusedColor = Color(0xFF363948)
    val backgroundColor = Color(0xFF1A1D2E)

    var currentValue by remember(value) { mutableStateOf(value) }
    var focusedIndex by remember { mutableStateOf(-1) }
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current

    // 当输入完成时触发回调
    LaunchedEffect(currentValue) {
        if (currentValue.length == length) {
            onComplete(currentValue)
        }
    }

    // 验证输入字符是否符合类型要求
    fun isValidChar(char: Char): Boolean {
        return when (captchaType) {
            CaptchaType.NUMBER -> char.isDigit()
            CaptchaType.LETTER -> char.isLetter()
            CaptchaType.MIXED -> char.isLetterOrDigit()
        }
    }

    // 获取键盘类型
    val keyboardType = when (captchaType) {
        CaptchaType.NUMBER -> KeyboardType.Number
        CaptchaType.LETTER -> KeyboardType.Text
        CaptchaType.MIXED -> KeyboardType.Text
    }

    Box(modifier = modifier) {
        // 透明的输入框，覆盖整个区域用于接收键盘输入
        BasicTextField(
            value = currentValue,
            onValueChange = { newValue ->
                // 过滤无效字符并限制长度
                val filteredValue = newValue.filter { isValidChar(it) }.take(length)
                currentValue = filteredValue
                onValueChange(filteredValue)
            },
            keyboardOptions = KeyboardOptions(
                keyboardType = keyboardType, imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(onDone = {
                keyboardController?.hide()
            }),
            textStyle = TextStyle(color = Color.Transparent),
            cursorBrush = SolidColor(Color.Transparent),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .focusRequester(focusRequester)
                .onFocusChanged { focusState ->
                    focusedIndex = if (focusState.isFocused) currentValue.length else -1
                })

        // 显示的验证码输入框
        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterHorizontally),
            modifier = Modifier.fillMaxWidth()
        ) {
            repeat(length) { index ->
                val char = currentValue.getOrNull(index)?.toString() ?: ""
                val isFocused = focusedIndex == index
                val hasValue = char.isNotEmpty()

                // 确定边框颜色
                val borderColor = when {
                    isFocused || hasValue -> focusedColor
                    else -> unFocusedColor
                }

                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .width(48.dp)
                        .height(48.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(backgroundColor)
                        .border(
                            width = 2.dp, color = borderColor, shape = RoundedCornerShape(8.dp)
                        )
                    // 移除点击处理，因为透明的 BasicTextField 会处理点击
                ) {
                    Text(
                        text = char,
                        fontSize = 30.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF9F2AF8),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }

    // 延迟自动聚焦，避免 BringIntoViewRequester 错误
    LaunchedEffect(Unit) {
        kotlinx.coroutines.delay(500) // 延迟500ms确保页面稳定
        try {
            focusRequester.requestFocus()
        } catch (e: Exception) {
            // 如果聚焦失败就忽略，用户可以手动点击
            println("CaptchaTextField: 自动聚焦失败，用户可以手动点击输入")
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF101321)
@Composable
fun CaptchaTextFieldPreview() {
    AnchorTheme {
        Box(
            modifier = Modifier
                .background(Color(0xFF101321))
                .padding(16.dp)
        ) {
            CaptchaTextField(
                length = 6,
                captchaType = CaptchaType.NUMBER,
                value = "241a",
                onValueChange = { },
                onComplete = { })
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF101321)
@Composable
fun CaptchaTextFieldLetterPreview() {
    AnchorTheme {
        Box(
            modifier = Modifier
                .background(Color(0xFF101321))
                .padding(16.dp)
        ) {
            CaptchaTextField(
                length = 4,
                captchaType = CaptchaType.LETTER,
                value = "AB",
                onValueChange = { },
                onComplete = { })
        }
    }
}
