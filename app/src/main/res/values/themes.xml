<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Anchor" parent="Theme.AppCompat.DayNight.NoActionBar">

        <!-- Use windowSplashScreenAnimatedIcon to add a drawable or an animated
             drawable. One of these is required. -->
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <!-- Required for animated icons. -->
        <item name="windowSplashScreenAnimationDuration">200</item>
        <item name="windowSplashScreenIconBackgroundColor">@android:color/transparent</item>

        <!-- Set the theme of the Activity that directly follows your splash
        screen. This is required. -->
        <item name="postSplashScreenTheme">@style/LaunchTheme</item>
        <item name="android:windowBackground">@color/background</item>
        <item name="android:navigationBarColor">@color/background</item>
    </style>

    <style name="Theme.App.Starting" parent="Theme.SplashScreen">
        <!-- Set the splash screen background, animated icon, and animation
        duration. -->
        <item name="windowSplashScreenBackground">@color/colorTheme</item>

        <!-- Use windowSplashScreenAnimatedIcon to add a drawable or an animated
             drawable. One of these is required. -->
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <!-- Required for animated icons. -->
        <item name="windowSplashScreenAnimationDuration">200</item>
        <item name="windowSplashScreenIconBackgroundColor">@android:color/transparent</item>

        <!-- Set the theme of the Activity that directly follows your splash
        screen. This is required. -->
        <item name="postSplashScreenTheme">@style/LaunchTheme</item>
    </style>

    <style name="LaunchTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowFullscreen">true</item>
<!--        <item name="android:windowBackground">@mipmap/bg_splash</item>-->
        <item name="android:windowBackground">@color/background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="PrimaryButton">
        <item name="android:background">@drawable/selector_primary_button</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/dp_14</item>
        <item name="android:gravity">center</item>
        <item name="textAllCaps">false</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp_40</item>
    </style>

    <style name="AnchorDialogTheme" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
    </style>
</resources>